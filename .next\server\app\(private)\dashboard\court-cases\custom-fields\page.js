(()=>{var a={};a.id=3948,a.ids=[3948],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3368:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3663:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14621:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\src\\\\app\\\\(private)\\\\dashboard\\\\court-cases\\\\custom-fields\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\court-cases\\custom-fields\\page.tsx","default")},15303:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},15952:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},16945:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},40284:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},47089:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},52701:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>Z});var d=c(21124),e=c(38301),f=c(23339);let g=(0,f.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var h=c(15952),i=c(42378),j=c(80974),k=c(3663);let l=(0,f.A)("hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]),m=(0,f.A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);var n=c(15303);let o=(0,f.A)("square-check-big",[["path",{d:"M21 10.656V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12.344",key:"2acyp4"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),p=(0,f.A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]]),q=(0,f.A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]),r=(0,f.A)("percent",[["line",{x1:"19",x2:"5",y1:"5",y2:"19",key:"1x9vlm"}],["circle",{cx:"6.5",cy:"6.5",r:"2.5",key:"4mh3h7"}],["circle",{cx:"17.5",cy:"17.5",r:"2.5",key:"1mdrzq"}]]),s=(0,f.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]),t=(0,f.A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]),u=(0,f.A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]),v=(0,f.A)("paperclip",[["path",{d:"m16 6-8.414 8.586a2 2 0 0 0 2.829 2.829l8.414-8.586a4 4 0 1 0-5.657-5.657l-8.379 8.551a6 6 0 1 0 8.485 8.485l8.379-8.551",key:"1miecu"}]]);var w=c(94684);let x=(0,f.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var y=c(3368);let z=(0,f.A)("grip-vertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]]),A=(0,f.A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);var B=c(76180),C=c(40284),D=c(72286),E=c(80517),F=c(47089);let G=[{value:"text",label:"Văn bản",icon:"\uD83D\uDCDD"},{value:"number",label:"Số",icon:"\uD83D\uDD22"},{value:"date",label:"Ng\xe0y",icon:"\uD83D\uDCC5"},{value:"datetime",label:"Ng\xe0y giờ",icon:"\uD83D\uDD50"},{value:"boolean",label:"Đ\xfang/Sai",icon:"☑️"},{value:"select",label:"Lựa chọn đơn",icon:"\uD83D\uDCCB"},{value:"multiselect",label:"Lựa chọn nhiều",icon:"\uD83D\uDCCB"},{value:"currency",label:"Tiền tệ",icon:"\uD83D\uDCB0"},{value:"percentage",label:"Phần trăm",icon:"\uD83D\uDCCA"},{value:"email",label:"Email",icon:"\uD83D\uDCE7"},{value:"phone",label:"Số điện thoại",icon:"\uD83D\uDCDE"},{value:"url",label:"Đường dẫn",icon:"\uD83D\uDD17"},{value:"textarea",label:"Văn bản d\xe0i",icon:"\uD83D\uDCC4"}];function H({isOpen:a,onClose:b,onSubmit:c,targetModel:f,existingFields:g=[]}){let[h,i]=(0,e.useState)({name:"",label:"",description:"",dataType:"text",config:{required:!1,showInList:!0,showInStats:!0,columnWidth:150,options:[]}}),[j,k]=(0,e.useState)({value:"",label:"",color:"#3B82F6"}),l=a=>g.some(b=>b.name.toLowerCase()===a.toLowerCase()),m="select"===h.dataType||"multiselect"===h.dataType;return a?(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Tạo trường t\xf9y chỉnh mới"}),(0,d.jsx)("button",{onClick:b,className:"p-2 text-gray-400 hover:text-gray-600 rounded-lg",children:(0,d.jsx)(F.A,{size:20})})]}),(0,d.jsxs)("form",{onSubmit:a=>(a.preventDefault(),h.name.trim()&&h.label.trim())?/^[a-zA-Z][a-zA-Z0-9_]*$/.test(h.name)?g.find(a=>a.name.toLowerCase()===h.name.toLowerCase())?void alert(`T\xean trường "${h.name}" đ\xe3 tồn tại. Vui l\xf2ng chọn t\xean kh\xe1c.`):void c(h):void alert("T\xean trường chỉ được chứa chữ c\xe1i, số v\xe0 dấu gạch dưới, bắt đầu bằng chữ c\xe1i"):void alert("Vui l\xf2ng nhập t\xean v\xe0 nh\xe3n hiển thị"),className:"p-6 space-y-6",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["T\xean trường ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("input",{type:"text",value:h.name,onChange:a=>i(b=>({...b,name:a.target.value})),className:`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${h.name&&l(h.name)?"border-red-300 focus:ring-red-500 bg-red-50":"border-gray-300 focus:ring-blue-500"}`,placeholder:"vd: priority_level",required:!0}),h.name&&l(h.name)?(0,d.jsx)("p",{className:"text-xs text-red-500 mt-1",children:"⚠️ T\xean trường n\xe0y đ\xe3 tồn tại. Vui l\xf2ng chọn t\xean kh\xe1c."}):(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Chỉ chữ c\xe1i, số v\xe0 dấu gạch dưới. Bắt đầu bằng chữ c\xe1i."})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Nh\xe3n hiển thị ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("input",{type:"text",value:h.label,onChange:a=>i(b=>({...b,label:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"vd: Mức độ ưu ti\xean",required:!0})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"M\xf4 tả"}),(0,d.jsx)("textarea",{value:h.description,onChange:a=>i(b=>({...b,description:a.target.value})),rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"M\xf4 tả về trường n\xe0y..."})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Kiểu dữ liệu ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-2",children:G.map(a=>(0,d.jsx)("button",{type:"button",onClick:()=>i(b=>({...b,dataType:a.value})),className:`p-3 border rounded-lg text-left transition-colors ${h.dataType===a.value?"border-blue-500 bg-blue-50 text-blue-700":"border-gray-300 hover:border-gray-400"}`,children:(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"text-lg",children:a.icon}),(0,d.jsx)("span",{className:"text-sm font-medium",children:a.label})]})},a.value))})]}),m&&(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["T\xf9y chọn ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsxs)("div",{className:"flex gap-2 mb-3",children:[(0,d.jsx)("input",{type:"text",value:j.value,onChange:a=>k(b=>({...b,value:a.target.value})),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Gi\xe1 trị (vd: high)"}),(0,d.jsx)("input",{type:"text",value:j.label,onChange:a=>k(b=>({...b,label:a.target.value})),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nh\xe3n hiển thị (vd: Cao)"}),(0,d.jsx)("input",{type:"color",value:j.color,onChange:a=>k(b=>({...b,color:a.target.value})),className:"w-12 h-10 border border-gray-300 rounded-lg"}),(0,d.jsx)("button",{type:"button",onClick:()=>j.value.trim()&&j.label.trim()?h.config.options.some(a=>a.value===j.value)?void alert("Gi\xe1 trị n\xe0y đ\xe3 tồn tại"):void(i(a=>({...a,config:{...a.config,options:[...a.config.options,{...j}]}})),k({value:"",label:"",color:"#3B82F6"})):void alert("Vui l\xf2ng nhập gi\xe1 trị v\xe0 nh\xe3n hiển thị"),className:"px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:(0,d.jsx)(x,{size:16})})]}),(0,d.jsx)("div",{className:"space-y-2 max-h-32 overflow-y-auto",children:h.config.options.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-gray-50 rounded-lg",children:[(0,d.jsx)("div",{className:"w-4 h-4 rounded",style:{backgroundColor:a.color}}),(0,d.jsx)("span",{className:"text-sm font-medium",children:a.label}),(0,d.jsxs)("span",{className:"text-sm text-gray-500",children:["(",a.value,")"]}),(0,d.jsx)("button",{type:"button",onClick:()=>{i(a=>({...a,config:{...a.config,options:a.config.options.filter((a,c)=>c!==b)}}))},className:"ml-auto p-1 text-red-600 hover:bg-red-50 rounded",children:(0,d.jsx)(C.A,{size:14})})]},b))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"C\xe0i đặt"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("label",{className:"flex items-center gap-2",children:[(0,d.jsx)("input",{type:"checkbox",checked:h.config.required,onChange:a=>i(b=>({...b,config:{...b.config,required:a.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsx)("span",{className:"text-sm",children:"Bắt buộc nhập"})]}),(0,d.jsxs)("label",{className:"flex items-center gap-2",children:[(0,d.jsx)("input",{type:"checkbox",checked:h.config.showInList,onChange:a=>i(b=>({...b,config:{...b.config,showInList:a.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsx)("span",{className:"text-sm",children:"Hiển thị trong danh s\xe1ch"})]}),(0,d.jsxs)("label",{className:"flex items-center gap-2",children:[(0,d.jsx)("input",{type:"checkbox",checked:h.config.showInStats,onChange:a=>i(b=>({...b,config:{...b.config,showInStats:a.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsx)("span",{className:"text-sm",children:"Hiển thị trong thống k\xea"})]})]})]}),(0,d.jsxs)("div",{className:"flex justify-end gap-3 pt-4 border-t border-gray-200",children:[(0,d.jsx)("button",{type:"button",onClick:b,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"}),(0,d.jsx)("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Tạo trường"})]})]})]})}):null}function I({isOpen:a,onClose:b,onSubmit:c,field:f}){var g;let[h,i]=(0,e.useState)({label:"",description:"",config:{required:!1,showInList:!0,showInStats:!0,columnWidth:150,options:[]}}),[j,k]=(0,e.useState)({value:"",label:"",color:"#3B82F6"}),l=f?.dataType==="select"||f?.dataType==="multiselect";return a&&f?(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Chỉnh sửa trường"}),(0,d.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,d.jsx)("span",{className:"text-lg",children:{text:"\uD83D\uDCDD",number:"\uD83D\uDD22",date:"\uD83D\uDCC5",datetime:"\uD83D\uDD50",boolean:"☑️",select:"\uD83D\uDCCB",multiselect:"\uD83D\uDCCB",currency:"\uD83D\uDCB0",percentage:"\uD83D\uDCCA",email:"\uD83D\uDCE7",phone:"\uD83D\uDCDE",url:"\uD83D\uDD17",textarea:"\uD83D\uDCC4",file:"\uD83D\uDCCE",json:"\uD83D\uDD27"}[f.dataType]||"❓"}),(0,d.jsxs)("span",{className:"text-sm text-gray-600",children:[f.name," (",{text:"Văn bản",number:"Số",date:"Ng\xe0y",datetime:"Ng\xe0y giờ",boolean:"Đ\xfang/Sai",select:"Lựa chọn đơn",multiselect:"Lựa chọn nhiều",currency:"Tiền tệ",percentage:"Phần trăm",email:"Email",phone:"Số điện thoại",url:"Đường dẫn",textarea:"Văn bản d\xe0i",file:"File đ\xednh k\xe8m",json:"Dữ liệu JSON"}[g=f.dataType]||g,")"]}),(0,d.jsx)("span",{className:`text-xs px-2 py-1 rounded ${f.isBuiltIn?"bg-blue-100 text-blue-600":"bg-green-100 text-green-600"}`,children:f.isBuiltIn?"Mặc định":"Đ\xe3 th\xeam"})]})]}),(0,d.jsx)("button",{onClick:b,className:"p-2 text-gray-400 hover:text-gray-600 rounded-lg",children:(0,d.jsx)(F.A,{size:20})})]}),(0,d.jsxs)("form",{onSubmit:a=>{if(a.preventDefault(),!h.label.trim())return void alert("Vui l\xf2ng nhập nh\xe3n hiển thị");c(h)},className:"p-6 space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Nh\xe3n hiển thị ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("input",{type:"text",value:h.label,onChange:a=>i(b=>({...b,label:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"vd: Mức độ ưu ti\xean",required:!0})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"M\xf4 tả"}),(0,d.jsx)("textarea",{value:h.description,onChange:a=>i(b=>({...b,description:a.target.value})),rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"M\xf4 tả về trường n\xe0y..."})]}),l&&!f.isBuiltIn&&(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"T\xf9y chọn"}),(0,d.jsxs)("div",{className:"flex gap-2 mb-3",children:[(0,d.jsx)("input",{type:"text",value:j.value,onChange:a=>k(b=>({...b,value:a.target.value})),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Gi\xe1 trị (vd: high)"}),(0,d.jsx)("input",{type:"text",value:j.label,onChange:a=>k(b=>({...b,label:a.target.value})),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nh\xe3n hiển thị (vd: Cao)"}),(0,d.jsx)("input",{type:"color",value:j.color,onChange:a=>k(b=>({...b,color:a.target.value})),className:"w-12 h-10 border border-gray-300 rounded-lg"}),(0,d.jsx)("button",{type:"button",onClick:()=>j.value.trim()&&j.label.trim()?h.config.options.some(a=>a.value===j.value)?void alert("Gi\xe1 trị n\xe0y đ\xe3 tồn tại"):void(i(a=>({...a,config:{...a.config,options:[...a.config.options,{...j}]}})),k({value:"",label:"",color:"#3B82F6"})):void alert("Vui l\xf2ng nhập gi\xe1 trị v\xe0 nh\xe3n hiển thị"),className:"px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:(0,d.jsx)(x,{size:16})})]}),(0,d.jsx)("div",{className:"space-y-2 max-h-32 overflow-y-auto",children:h.config.options.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-gray-50 rounded-lg",children:[(0,d.jsx)("div",{className:"w-4 h-4 rounded",style:{backgroundColor:a.color}}),(0,d.jsx)("span",{className:"text-sm font-medium",children:a.label}),(0,d.jsxs)("span",{className:"text-sm text-gray-500",children:["(",a.value,")"]}),(0,d.jsx)("button",{type:"button",onClick:()=>{i(a=>({...a,config:{...a.config,options:a.config.options.filter((a,c)=>c!==b)}}))},className:"ml-auto p-1 text-red-600 hover:bg-red-50 rounded",children:(0,d.jsx)(C.A,{size:14})})]},b))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"C\xe0i đặt"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("label",{className:"flex items-center gap-2",children:[(0,d.jsx)("input",{type:"checkbox",checked:h.config.required,onChange:a=>i(b=>({...b,config:{...b.config,required:a.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsx)("span",{className:"text-sm",children:"Bắt buộc nhập"})]}),(0,d.jsxs)("label",{className:"flex items-center gap-2",children:[(0,d.jsx)("input",{type:"checkbox",checked:h.config.showInList,onChange:a=>i(b=>({...b,config:{...b.config,showInList:a.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsx)("span",{className:"text-sm",children:"Hiển thị trong danh s\xe1ch"})]}),(0,d.jsxs)("label",{className:"flex items-center gap-2",children:[(0,d.jsx)("input",{type:"checkbox",checked:h.config.showInStats,onChange:a=>i(b=>({...b,config:{...b.config,showInStats:a.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsx)("span",{className:"text-sm",children:"Hiển thị trong thống k\xea"})]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Độ rộng cột (pixels)"}),(0,d.jsx)("input",{type:"number",value:h.config.columnWidth,onChange:a=>i(b=>({...b,config:{...b.config,columnWidth:parseInt(a.target.value)||150}})),className:"w-32 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",min:"50",max:"500"})]}),(0,d.jsxs)("div",{className:"flex justify-end gap-3 pt-4 border-t border-gray-200",children:[(0,d.jsx)("button",{type:"button",onClick:b,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"}),(0,d.jsx)("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Cập nhật"})]})]})]})}):null}var J=c(25816);function K({targetModel:a="CourtCase",onFieldsChange:b}){let[c,f]=(0,e.useState)([]),[g,h]=(0,e.useState)([]),[i,F]=(0,e.useState)(null),[G,K]=(0,e.useState)([]),[L,M]=(0,e.useState)(!0),[N,O]=(0,e.useState)(!1),[P,Q]=(0,e.useState)(null),[R,S]=(0,e.useState)(null),[T,U]=(0,e.useState)(""),[V,W]=(0,e.useState)("all"),[X,Y]=(0,e.useState)(!1),{hasPermission:Z}=(0,J.S)(),$=async()=>{try{M(!0);let c=localStorage.getItem("sessionToken")||"",[d,e,g]=await Promise.all([D.A.getCustomFields(a,c),E.A.getFieldConfiguration(a,c),D.A.getFieldsInDatabase(a).catch(()=>({payload:{data:{fields:[]}}}))]);if(d.payload.success&&e.payload.success){f(d.payload.fields),F(e.payload.configuration),g.payload?.data?.fields&&K(g.payload.data.fields);let a=[];d.payload.fields.forEach(b=>{a.push({...b,canEdit:!0,canDelete:!0})}),a.sort((a,b)=>a.config.sortOrder-b.config.sortOrder),h(a),b?.(d.payload.fields)}}catch(a){console.error("Error fetching fields and configuration:",a),j.oR.error("Lỗi khi tải danh s\xe1ch trường t\xf9y chỉnh")}finally{M(!1)}},_=async b=>{try{let c=localStorage.getItem("sessionToken")||"",d=await D.A.createCustomField({...b,targetModel:a},c);d.payload.success?(j.oR.success("Tạo trường t\xf9y chỉnh th\xe0nh c\xf4ng"),O(!1),$()):j.oR.error(d.payload.message||"Kh\xf4ng thể tạo trường t\xf9y chỉnh")}catch(a){console.error("Error creating custom field:",a),a.payload?.message?j.oR.error(a.payload.message):a.message?j.oR.error(a.message):j.oR.error("Lỗi khi tạo trường t\xf9y chỉnh")}},aa=async(a,b)=>{try{let c=localStorage.getItem("sessionToken")||"",d=await D.A.updateCustomField(a,b,c);d.payload.success?(j.oR.success("Cập nhật trường t\xf9y chỉnh th\xe0nh c\xf4ng"),Q(null),$()):j.oR.error(d.payload.message||"Kh\xf4ng thể cập nhật trường t\xf9y chỉnh")}catch(a){console.error("Error updating custom field:",a),j.oR.error("Lỗi khi cập nhật trường t\xf9y chỉnh")}},ab=async a=>{let b=a.isDefault?"trường cơ bản":"trường t\xf9y chỉnh";if(confirm(`Bạn c\xf3 chắc chắn muốn x\xf3a ${b} "${a.label}"? Dữ liệu đ\xe3 nhập sẽ kh\xf4ng bị mất nhưng trường sẽ kh\xf4ng hiển thị nữa.`))try{if(a.isDefault){let c=g.filter(b=>b._id!==a._id);h(c),j.oR.success(`X\xf3a ${b} th\xe0nh c\xf4ng`)}else{let b=localStorage.getItem("sessionToken")||"",c=await D.A.deleteCustomField(a._id,b);c.payload.success?(j.oR.success("X\xf3a trường t\xf9y chỉnh th\xe0nh c\xf4ng"),$()):j.oR.error(c.payload.message||"Kh\xf4ng thể x\xf3a trường t\xf9y chỉnh")}}catch(a){console.error("Error deleting field:",a),j.oR.error(`Lỗi khi x\xf3a ${b}`)}},ac=async a=>{try{await aa(a._id,{config:{...a.config,showInList:!a.config.showInList}})}catch(a){console.error("Error toggling field visibility:",a),j.oR.error("Lỗi khi cập nhật hiển thị trường")}},ad=a=>{a.preventDefault(),a.dataTransfer.dropEffect="move"},ae=async(b,c)=>{if(b.preventDefault(),!R||R===c)return void S(null);let d=g.findIndex(a=>a._id===R),e=g.findIndex(a=>a._id===c);if(-1===d||-1===e)return void S(null);let f=[...g],[h]=f.splice(d,1);f.splice(e,0,h);let i=f.map((a,b)=>({fieldName:a.name,sortOrder:b}));try{let b=localStorage.getItem("sessionToken")||"";await E.A.updateFieldOrder({targetModel:a,fieldOrders:i},b);let c=f.filter(a=>!a.isDefault).map((a,b)=>({id:a._id,sortOrder:b}));c.length>0&&await D.A.updateFieldsOrder(c,b),await $(),j.oR.success("Cập nhật thứ tự trường th\xe0nh c\xf4ng")}catch(a){console.error("Error updating field order:",a),j.oR.error("Lỗi khi cập nhật thứ tự trường")}S(null)},af=g.filter(a=>{let b=""===T||a.name.toLowerCase().includes(T.toLowerCase())||a.label.toLowerCase().includes(T.toLowerCase()),c="all"===V||"custom"===V||"visible"===V&&a.config.showInList||"hidden"===V&&!a.config.showInList;return b&&c});return L?(0,d.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 flex items-center gap-2",children:[(0,d.jsx)(w.A,{size:20}),"Quản l\xfd trường t\xf9y chỉnh"]}),(0,d.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:["Tạo v\xe0 quản l\xfd c\xe1c trường dữ liệu t\xf9y chỉnh cho ","CourtCase"===a?"vụ việc t\xf2a \xe1n":a]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsxs)("button",{onClick:()=>Y(!X),className:"flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,d.jsx)(k.A,{size:16}),X?"Ẩn th\xf4ng tin DB":"Xem th\xf4ng tin DB"]}),Z("custom_fields_create")&&(0,d.jsxs)("button",{onClick:()=>O(!0),className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,d.jsx)(x,{size:16}),"Th\xeam trường mới"]})]})]}),g.length>0&&(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,d.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,d.jsx)(w.A,{size:20,className:"text-blue-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-blue-900",children:g.length}),(0,d.jsx)("div",{className:"text-sm text-blue-700",children:"Tổng số trường"})]})]})}),(0,d.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,d.jsx)(x,{size:20,className:"text-green-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-green-900",children:g.filter(a=>!a.isDefault).length}),(0,d.jsx)("div",{className:"text-sm text-green-700",children:"Trường đ\xe3 th\xeam"})]})]})}),(0,d.jsx)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,d.jsx)(y.A,{size:20,className:"text-purple-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-purple-900",children:g.filter(a=>a.config.showInList).length}),(0,d.jsx)("div",{className:"text-sm text-purple-700",children:"Đang hiển thị"})]})]})})]}),X&&G.length>0&&(0,d.jsxs)("div",{className:"mb-6 p-4 bg-gray-50 border border-gray-200 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsxs)("h4",{className:"text-md font-semibold text-gray-900 flex items-center gap-2",children:[(0,d.jsx)(k.A,{size:18}),"Trường đang c\xf3 trong Database (",G.length,")"]}),(0,d.jsx)("button",{onClick:()=>Y(!1),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-60 overflow-y-auto",children:G.map((a,b)=>(0,d.jsxs)("div",{className:"bg-white p-3 rounded border",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,d.jsx)("span",{className:"font-medium text-sm text-gray-900",children:a.name}),(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[a.hasConfig?(0,d.jsx)("span",{className:"px-2 py-1 text-xs bg-green-100 text-green-800 rounded",children:"C\xf3 config"}):(0,d.jsx)("span",{className:"px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded",children:"Kh\xf4ng config"}),!a.isActive&&(0,d.jsx)("span",{className:"px-2 py-1 text-xs bg-red-100 text-red-800 rounded",children:"Inactive"})]})]}),(0,d.jsxs)("div",{className:"text-xs text-gray-600 mb-1",children:[(0,d.jsx)("span",{className:"font-medium",children:"Nh\xe3n:"})," ",a.label]}),(0,d.jsxs)("div",{className:"text-xs text-gray-600 mb-1",children:[(0,d.jsx)("span",{className:"font-medium",children:"Kiểu:"})," ",a.dataType]}),(0,d.jsxs)("div",{className:"text-xs text-gray-600 mb-2",children:[(0,d.jsx)("span",{className:"font-medium",children:"Sử dụng:"})," ",a.usageCount,"/",a.totalDocuments,"(",(a.usageCount/a.totalDocuments*100).toFixed(1),"%)"]}),a.sampleValues&&a.sampleValues.length>0&&(0,d.jsxs)("div",{className:"text-xs text-gray-500",children:[(0,d.jsx)("span",{className:"font-medium",children:"Mẫu:"})," ",a.sampleValues.slice(0,2).join(", "),a.sampleValues.length>2&&"..."]})]},b))})]}),g.length>0&&(0,d.jsxs)("div",{className:"flex flex-wrap gap-2 mb-6",children:[(0,d.jsxs)("button",{onClick:()=>{h(g.map(a=>({...a,config:{...a.config,showInList:!0}}))),j.oR.success("Hiển thị tất cả trường th\xe0nh c\xf4ng")},className:"flex items-center gap-1 px-3 py-1 text-sm bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors",children:[(0,d.jsx)(y.A,{size:14}),"Hiển thị tất cả"]}),(0,d.jsx)("button",{onClick:()=>{h(g.map(a=>({...a,config:{...a.config,showInList:!1}}))),j.oR.success("Ẩn tất cả trường th\xe0nh c\xf4ng")},className:"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:"\uD83D\uDE48 Ẩn tất cả"})]}),g.length>0&&(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 mb-6",children:[(0,d.jsx)("div",{className:"flex-1",children:(0,d.jsx)("input",{type:"text",placeholder:"T\xecm kiếm trường theo t\xean hoặc nh\xe3n...",value:T,onChange:a=>U(a.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})}),(0,d.jsxs)("select",{value:V,onChange:a=>W(a.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,d.jsx)("option",{value:"all",children:"Tất cả trường"}),(0,d.jsx)("option",{value:"custom",children:"Chỉ trường đ\xe3 th\xeam"}),(0,d.jsx)("option",{value:"visible",children:"Chỉ trường hiển thị"}),(0,d.jsx)("option",{value:"hidden",children:"Chỉ trường ẩn"})]})]}),0===g.length?(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(w.A,{size:48,className:"mx-auto text-gray-400 mb-4"}),(0,d.jsx)("p",{className:"text-gray-500 mb-4",children:"Đang tải danh s\xe1ch trường..."})]}):0===af.length?(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(w.A,{size:48,className:"mx-auto text-gray-400 mb-4"}),(0,d.jsx)("p",{className:"text-gray-500 mb-4",children:"Kh\xf4ng t\xecm thấy trường n\xe0o ph\xf9 hợp"}),(0,d.jsx)("button",{onClick:()=>{U(""),W("all")},className:"text-blue-600 hover:text-blue-700 font-medium",children:"X\xf3a bộ lọc"})]}):(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3 mb-4",children:(0,d.jsxs)("div",{className:"flex items-start gap-2",children:[(0,d.jsx)("span",{className:"text-green-500 mt-0.5",children:"ℹ️"}),(0,d.jsxs)("div",{className:"text-sm text-green-700",children:[(0,d.jsx)("strong",{children:"Hướng dẫn:"})," Tất cả c\xe1c trường đều c\xf3 thể k\xe9o thả để sắp xếp thứ tự. Trường cơ bản (m\xe0u xanh dương) v\xe0 trường t\xf9y chỉnh (m\xe0u xanh l\xe1) đều c\xf3 thể di chuyển."]})]})}),af.map(a=>{var b;return(0,d.jsxs)("div",{draggable:!0,onDragStart:b=>{S(a._id),b.dataTransfer.effectAllowed="move"},onDragOver:ad,onDrop:b=>ae(b,a._id),className:`flex items-center gap-4 p-4 border rounded-lg hover:bg-gray-50 transition-colors cursor-move ${R===a._id?"opacity-50":""} ${!a.config.showInList?"bg-gray-50 border-gray-200":"border-gray-300"} border-l-4 border-l-green-500`,children:[(0,d.jsx)(z,{size:16,className:"text-gray-400",title:"K\xe9o thả để sắp xếp thứ tự"}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"text-lg",children:(a=>{switch(a){case"text":return(0,d.jsx)(k.A,{size:16,className:"text-blue-500"});case"number":return(0,d.jsx)(l,{size:16,className:"text-green-500"});case"date":return(0,d.jsx)(m,{size:16,className:"text-purple-500"});case"datetime":return(0,d.jsx)(n.A,{size:16,className:"text-purple-600"});case"boolean":return(0,d.jsx)(o,{size:16,className:"text-orange-500"});case"select":return(0,d.jsx)(p,{size:16,className:"text-indigo-500"});case"multiselect":return(0,d.jsx)(p,{size:16,className:"text-indigo-600"});case"currency":return(0,d.jsx)(q,{size:16,className:"text-green-600"});case"percentage":return(0,d.jsx)(r,{size:16,className:"text-yellow-500"});case"email":return(0,d.jsx)(s,{size:16,className:"text-red-500"});case"phone":return(0,d.jsx)(t,{size:16,className:"text-blue-600"});case"url":return(0,d.jsx)(u,{size:16,className:"text-cyan-500"});case"textarea":return(0,d.jsx)(k.A,{size:16,className:"text-gray-500"});case"file":return(0,d.jsx)(v,{size:16,className:"text-gray-600"});case"json":return(0,d.jsx)(w.A,{size:16,className:"text-gray-700"});default:return(0,d.jsx)(k.A,{size:16,className:"text-gray-400"})}})(a.dataType)}),(0,d.jsx)("span",{className:"font-medium text-gray-900",children:a.label}),(0,d.jsx)("span",{className:"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded",children:{text:"Văn bản",number:"Số",date:"Ng\xe0y",datetime:"Ng\xe0y giờ",boolean:"Đ\xfang/Sai",select:"Lựa chọn đơn",multiselect:"Lựa chọn nhiều",currency:"Tiền tệ",percentage:"Phần trăm",email:"Email",phone:"Số điện thoại",url:"Đường dẫn",textarea:"Văn bản d\xe0i",file:"File đ\xednh k\xe8m",json:"Dữ liệu JSON"}[b=a.dataType]||b}),a.config.required&&(0,d.jsx)("span",{className:"text-xs bg-red-100 text-red-600 px-2 py-1 rounded",children:"Bắt buộc"}),(0,d.jsx)("span",{className:`text-xs px-2 py-1 rounded ${a.isDefault?"bg-blue-100 text-blue-600":"bg-green-100 text-green-600"}`,children:a.isDefault?"Cơ bản":"Đ\xe3 th\xeam"})]}),a.description&&(0,d.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:a.description})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("button",{onClick:()=>ac(a),className:`p-2 rounded-lg transition-colors ${a.config.showInList?"text-green-600 hover:bg-green-50":"text-gray-400 hover:bg-gray-100"}`,title:a.config.showInList?"Ẩn khỏi danh s\xe1ch":"Hiển thị trong danh s\xe1ch",children:a.config.showInList?(0,d.jsx)(y.A,{size:16}):(0,d.jsx)(A,{size:16})}),Z("custom_fields_edit")&&(0,d.jsx)("button",{onClick:()=>Q(a),className:"p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",title:"Chỉnh sửa",children:(0,d.jsx)(B.A,{size:16})}),Z("custom_fields_delete")&&(0,d.jsx)("button",{onClick:()=>ab(a),className:"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors",title:"X\xf3a",children:(0,d.jsx)(C.A,{size:16})})]})]},a._id)}),0===g.length&&(0,d.jsxs)("div",{className:"text-center py-6 border-2 border-dashed border-gray-300 rounded-lg mt-4",children:[(0,d.jsx)("p",{className:"text-gray-500 mb-2",children:"Chưa c\xf3 trường n\xe0o được th\xeam"}),Z("custom_fields_create")&&(0,d.jsx)("button",{onClick:()=>O(!0),className:"text-blue-600 hover:text-blue-700 font-medium",children:"Tạo trường đầu ti\xean"})]})]}),(0,d.jsx)(H,{isOpen:N,onClose:()=>O(!1),onSubmit:_,targetModel:a,existingFields:c.map(a=>({name:a.name,label:a.label}))}),P&&(0,d.jsx)(I,{isOpen:!0,onClose:()=>Q(null),onSubmit:a=>{aa(P._id,a)},field:P})]})}let L=(0,f.A)("chart-pie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]]),M=(0,f.A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]),N=(0,f.A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);function O({targetModel:a="CourtCase",fields:b}){let[c,f]=(0,e.useState)({}),[g,i]=(0,e.useState)(!0),k=async()=>{try{i(!0);let b=localStorage.getItem("sessionToken")||"",c=await D.A.getCustomFieldStats(a,b);c.payload.success&&f(c.payload.stats)}catch(a){console.error("Error fetching custom field stats:",a),j.oR.error("Lỗi khi tải thống k\xea trường t\xf9y chỉnh")}finally{i(!1)}},l=a=>new Intl.NumberFormat("vi-VN").format(a),m=a=>new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"}).format(a);if(g)return(0,d.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})});let n=Object.entries(c);return 0===n.length?(0,d.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(h.A,{size:48,className:"mx-auto text-gray-400 mb-4"}),(0,d.jsx)("p",{className:"text-gray-500 mb-4",children:"Chưa c\xf3 thống k\xea cho trường t\xf9y chỉnh"}),(0,d.jsx)("p",{className:"text-sm text-gray-400",children:"Th\xeam dữ liệu v\xe0o c\xe1c trường t\xf9y chỉnh để xem thống k\xea"})]})}):(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,d.jsx)(h.A,{size:24,className:"text-blue-600"}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Thống k\xea trường t\xf9y chỉnh"})]}),(0,d.jsx)("p",{className:"text-gray-600",children:"Thống k\xea tự động được t\xednh to\xe1n dựa tr\xean dữ liệu trong c\xe1c trường t\xf9y chỉnh"})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:n.map(([a,c])=>{let e=b.find(b=>b.name===a);return(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,d.jsxs)("div",{className:"p-2 bg-blue-100 rounded-lg",children:["distribution"===c.type&&(0,d.jsx)(L,{size:20,className:"text-blue-600"}),"numeric"===c.type&&(0,d.jsx)(M,{size:20,className:"text-blue-600"}),"boolean"===c.type&&(0,d.jsx)(N,{size:20,className:"text-blue-600"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-semibold text-gray-900",children:c.label}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:e?.dataType==="currency"?"Tiền tệ":e?.dataType==="percentage"?"Phần trăm":e?.dataType==="select"?"Lựa chọn đơn":e?.dataType==="multiselect"?"Lựa chọn nhiều":e?.dataType==="boolean"?"Đ\xfang/Sai":e?.dataType==="number"?"Số":"Kh\xe1c"})]})]}),"distribution"===c.type&&Array.isArray(c.data)&&(0,d.jsx)("div",{className:"space-y-3",children:c.data.map((a,b)=>{let e=c.data.reduce((a,b)=>a+b.count,0),f=e>0?a.count/e*100:0;return(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("div",{className:"w-3 h-3 rounded-full bg-blue-500"}),(0,d.jsx)("span",{className:"text-sm font-medium text-gray-700",children:a._id||"Kh\xf4ng x\xe1c định"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:l(a.count)}),(0,d.jsxs)("span",{className:"text-xs text-gray-500",children:["(",f.toFixed(1),"%)"]})]})]},b)})}),"numeric"===c.type&&c.data&&(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,d.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:e?.dataType==="currency"?m(c.data.total||0):l(c.data.total||0)}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Tổng"})]}),(0,d.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,d.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:e?.dataType==="currency"?m(c.data.avg||0):l(c.data.avg||0)}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Trung b\xecnh"})]}),(0,d.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,d.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:e?.dataType==="currency"?m(c.data.min||0):l(c.data.min||0)}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Nhỏ nhất"})]}),(0,d.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,d.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:e?.dataType==="currency"?m(c.data.max||0):l(c.data.max||0)}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Lớn nhất"})]})]}),"boolean"===c.type&&Array.isArray(c.data)&&(0,d.jsx)("div",{className:"space-y-3",children:c.data.map((a,b)=>{let e=c.data.reduce((a,b)=>a+b.count,0),f=e>0?a.count/e*100:0;return(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("div",{className:`w-3 h-3 rounded-full ${a._id?"bg-green-500":"bg-red-500"}`}),(0,d.jsx)("span",{className:"text-sm font-medium text-gray-700",children:a._id?"C\xf3":"Kh\xf4ng"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:l(a.count)}),(0,d.jsxs)("span",{className:"text-xs text-gray-500",children:["(",f.toFixed(1),"%)"]})]})]},b)})})]},a)})}),(0,d.jsx)("div",{className:"text-center",children:(0,d.jsx)("button",{onClick:k,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"L\xe0m mới thống k\xea"})})]})}var P=c(16945),Q=c(90828);function R({isOpen:a,onClose:b,onSubmit:c,availableFields:f}){let[g,h]=(0,e.useState)(""),[i,j]=(0,e.useState)({enabled:!0,warningDays:30,dangerDays:7,safeDays:90,showInList:!0,showCountdownBadge:!0,showColorWarning:!0,hideExpired:!1,emailNotification:{enabled:!1,daysBefore:7}}),k=a=>{if(a.preventDefault(),!g)return void alert("Vui l\xf2ng chọn trường ng\xe0y");if(i.warningDays&&i.dangerDays&&i.dangerDays>=i.warningDays)return void alert("Số ng\xe0y nguy hiểm phải nhỏ hơn số ng\xe0y cảnh b\xe1o");if(i.safeDays&&i.warningDays&&i.safeDays<=i.warningDays)return void alert("Số ng\xe0y an to\xe0n phải lớn hơn số ng\xe0y cảnh b\xe1o");let b=f.find(a=>a.name===g);b&&c(b.name,b.isDefault,i)};return a?(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,d.jsx)(n.A,{size:20,className:"text-blue-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Tạo cấu h\xecnh đếm ngược"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Thiết lập cảnh b\xe1o hết hạn cho trường ng\xe0y (GMT+7)"}),(0,d.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded p-2 mt-2",children:(0,d.jsxs)("p",{className:"text-xs text-blue-700",children:["\uD83D\uDCA1 ",(0,d.jsx)("strong",{children:"Lưu \xfd:"})," Ng\xe0y được chọn l\xe0 ng\xe0y bắt đầu đếm ngược, hệ thống sẽ cộng số ng\xe0y cảnh b\xe1o để t\xednh ng\xe0y hết hạn"]})})]})]}),(0,d.jsx)("button",{onClick:b,className:"text-gray-500 hover:text-gray-700 transition-colors p-2 hover:bg-gray-100 rounded-lg",children:(0,d.jsx)(F.A,{size:20})})]}),(0,d.jsxs)("form",{onSubmit:k,className:"p-6 space-y-6 overflow-y-auto max-h-[calc(90vh-140px)]",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Chọn trường ng\xe0y ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsxs)("select",{value:g,onChange:a=>h(a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0,children:[(0,d.jsx)("option",{value:"",children:"-- Chọn trường ng\xe0y --"}),f.map(a=>(0,d.jsxs)("option",{value:a.name,children:[a.label," ",a.isDefault?"(Cơ bản)":"(T\xf9y chỉnh)"]},a.name))]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("h4",{className:"text-md font-medium text-gray-900 flex items-center gap-2",children:[(0,d.jsx)(P.A,{size:16}),"Cấu h\xecnh cảnh b\xe1o"]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Số ng\xe0y an to\xe0n"}),(0,d.jsx)("input",{type:"number",min:"1",max:"365",value:i.safeDays||90,onChange:a=>j(b=>({...b,safeDays:parseInt(a.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Hiển thị m\xe0u xanh khi c\xf2n nhiều hơn X ng\xe0y"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Số ng\xe0y cảnh b\xe1o"}),(0,d.jsx)("input",{type:"number",min:"1",max:"365",value:i.warningDays||30,onChange:a=>j(b=>({...b,warningDays:parseInt(a.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Hiển thị cảnh b\xe1o v\xe0ng khi c\xf2n X ng\xe0y"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Số ng\xe0y nguy hiểm"}),(0,d.jsx)("input",{type:"number",min:"1",max:"365",value:i.dangerDays||7,onChange:a=>j(b=>({...b,dangerDays:parseInt(a.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Hiển thị cảnh b\xe1o đỏ khi c\xf2n X ng\xe0y"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h4",{className:"text-md font-medium text-gray-900",children:"Cấu h\xecnh hiển thị"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("label",{className:"flex items-center gap-3",children:[(0,d.jsx)("input",{type:"checkbox",checked:i.enabled,onChange:a=>j(b=>({...b,enabled:a.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"Bật đếm ngược"}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"K\xedch hoạt t\xednh năng đếm ngược cho trường n\xe0y"})]})]}),(0,d.jsxs)("label",{className:"flex items-center gap-3",children:[(0,d.jsx)("input",{type:"checkbox",checked:i.showCountdownBadge,onChange:a=>j(b=>({...b,showCountdownBadge:a.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"Hiển thị badge đếm ngược"}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"Hiển thị đếm ngược chi tiết (ng\xe0y, giờ, ph\xfat, gi\xe2y) theo GMT+7"})]})]}),(0,d.jsxs)("label",{className:"flex items-center gap-3",children:[(0,d.jsx)("input",{type:"checkbox",checked:i.showColorWarning,onChange:a=>j(b=>({...b,showColorWarning:a.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"Hiển thị m\xe0u cảnh b\xe1o"}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"Thay đổi m\xe0u sắc theo trạng th\xe1i (xanh/v\xe0ng/đỏ)"})]})]}),(0,d.jsxs)("label",{className:"flex items-center gap-3",children:[(0,d.jsx)("input",{type:"checkbox",checked:i.hideExpired,onChange:a=>j(b=>({...b,hideExpired:a.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"Ẩn nếu qu\xe1 hạn"}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"Tự động ẩn badge khi đ\xe3 qu\xe1 hạn"})]})]})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("h4",{className:"text-md font-medium text-gray-900 flex items-center gap-2",children:[(0,d.jsx)(s,{size:16}),"Th\xf4ng b\xe1o email (T\xf9y chọn)"]}),(0,d.jsxs)("label",{className:"flex items-center gap-3",children:[(0,d.jsx)("input",{type:"checkbox",checked:i.emailNotification?.enabled,onChange:a=>j(b=>({...b,emailNotification:{...b.emailNotification,enabled:a.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"Gửi email cảnh b\xe1o"}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"Tự động gửi email th\xf4ng b\xe1o trước hạn"})]})]}),i.emailNotification?.enabled&&(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Gửi email trước (ng\xe0y)"}),(0,d.jsx)("input",{type:"number",min:"1",max:"365",value:i.emailNotification?.daysBefore||7,onChange:a=>j(b=>({...b,emailNotification:{...b.emailNotification,daysBefore:parseInt(a.target.value)}})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,d.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[(0,d.jsx)("h5",{className:"text-sm font-medium text-gray-900 mb-2",children:"Xem trước:"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Ng\xe0y:"}),(0,d.jsx)("span",{className:"text-sm",children:"25/12/2024"})]}),i.showCountdownBadge&&(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Badge:"}),(0,d.jsx)("span",{className:"inline-flex items-center gap-1 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 border border-yellow-200 rounded-full",children:"⏰ C\xf2n 15 ng\xe0y"})]})]})]})]}),(0,d.jsxs)("div",{className:"flex justify-end gap-4 p-6 border-t border-gray-200",children:[(0,d.jsx)("button",{type:"button",onClick:()=>{h(""),j({enabled:!0,warningDays:30,dangerDays:7,safeDays:90,showInList:!0,showCountdownBadge:!0,showColorWarning:!0,hideExpired:!1,emailNotification:{enabled:!1,daysBefore:7}}),b()},className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"}),(0,d.jsx)("button",{type:"submit",form:"countdown-form",onClick:k,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Tạo cấu h\xecnh"})]})]})}):null}function S({isOpen:a,onClose:b,onSubmit:c,countdown:f,getFieldLabel:g}){let[h,i]=(0,e.useState)({}),j=a=>{if(a.preventDefault(),f){if(h.warningDays&&h.dangerDays&&h.dangerDays>=h.warningDays)return void alert("Số ng\xe0y nguy hiểm phải nhỏ hơn số ng\xe0y cảnh b\xe1o");if(h.safeDays&&h.warningDays&&h.safeDays<=h.warningDays)return void alert("Số ng\xe0y an to\xe0n phải lớn hơn số ng\xe0y cảnh b\xe1o");c(f._id,h)}};return a&&f?(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,d.jsx)(n.A,{size:20,className:"text-blue-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Chỉnh sửa cấu h\xecnh đếm ngược"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:g(f.fieldName)})]})]}),(0,d.jsx)("button",{onClick:b,className:"text-gray-500 hover:text-gray-700 transition-colors p-2 hover:bg-gray-100 rounded-lg",children:(0,d.jsx)(F.A,{size:20})})]}),(0,d.jsxs)("form",{onSubmit:j,className:"p-6 space-y-6 overflow-y-auto max-h-[calc(90vh-140px)]",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("h4",{className:"text-md font-medium text-gray-900 flex items-center gap-2",children:[(0,d.jsx)(P.A,{size:16}),"Cấu h\xecnh cảnh b\xe1o"]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Số ng\xe0y an to\xe0n"}),(0,d.jsx)("input",{type:"number",min:"1",max:"365",value:h.safeDays||90,onChange:a=>i(b=>({...b,safeDays:parseInt(a.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Hiển thị m\xe0u xanh khi c\xf2n nhiều hơn X ng\xe0y"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Số ng\xe0y cảnh b\xe1o"}),(0,d.jsx)("input",{type:"number",min:"1",max:"365",value:h.warningDays||30,onChange:a=>i(b=>({...b,warningDays:parseInt(a.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Hiển thị cảnh b\xe1o v\xe0ng khi c\xf2n X ng\xe0y"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Số ng\xe0y nguy hiểm"}),(0,d.jsx)("input",{type:"number",min:"1",max:"365",value:h.dangerDays||7,onChange:a=>i(b=>({...b,dangerDays:parseInt(a.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Hiển thị cảnh b\xe1o đỏ khi c\xf2n X ng\xe0y"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h4",{className:"text-md font-medium text-gray-900",children:"Cấu h\xecnh hiển thị"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("label",{className:"flex items-center gap-3",children:[(0,d.jsx)("input",{type:"checkbox",checked:h.enabled,onChange:a=>i(b=>({...b,enabled:a.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"Bật đếm ngược"}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"K\xedch hoạt t\xednh năng đếm ngược cho trường n\xe0y"})]})]}),(0,d.jsxs)("label",{className:"flex items-center gap-3",children:[(0,d.jsx)("input",{type:"checkbox",checked:h.showCountdownBadge,onChange:a=>i(b=>({...b,showCountdownBadge:a.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"Hiển thị badge đếm ngược"}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:'Hiển thị badge "C\xf2n X ng\xe0y" trong danh s\xe1ch'})]})]}),(0,d.jsxs)("label",{className:"flex items-center gap-3",children:[(0,d.jsx)("input",{type:"checkbox",checked:h.showColorWarning,onChange:a=>i(b=>({...b,showColorWarning:a.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"Hiển thị m\xe0u cảnh b\xe1o"}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"Thay đổi m\xe0u sắc theo trạng th\xe1i (xanh/v\xe0ng/đỏ)"})]})]}),(0,d.jsxs)("label",{className:"flex items-center gap-3",children:[(0,d.jsx)("input",{type:"checkbox",checked:h.hideExpired,onChange:a=>i(b=>({...b,hideExpired:a.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"Ẩn nếu qu\xe1 hạn"}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"Tự động ẩn badge khi đ\xe3 qu\xe1 hạn"})]})]})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("h4",{className:"text-md font-medium text-gray-900 flex items-center gap-2",children:[(0,d.jsx)(s,{size:16}),"Th\xf4ng b\xe1o email (T\xf9y chọn)"]}),(0,d.jsxs)("label",{className:"flex items-center gap-3",children:[(0,d.jsx)("input",{type:"checkbox",checked:h.emailNotification?.enabled,onChange:a=>i(b=>({...b,emailNotification:{...b.emailNotification,enabled:a.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"Gửi email cảnh b\xe1o"}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"Tự động gửi email th\xf4ng b\xe1o trước hạn"})]})]}),h.emailNotification?.enabled&&(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Gửi email trước (ng\xe0y)"}),(0,d.jsx)("input",{type:"number",min:"1",max:"365",value:h.emailNotification?.daysBefore||7,onChange:a=>i(b=>({...b,emailNotification:{...b.emailNotification,daysBefore:parseInt(a.target.value)}})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,d.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[(0,d.jsx)("h5",{className:"text-sm font-medium text-gray-900 mb-2",children:"Xem trước:"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Ng\xe0y:"}),(0,d.jsx)("span",{className:"text-sm",children:"25/12/2024"})]}),h.showCountdownBadge&&(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Badge:"}),(0,d.jsx)("span",{className:"inline-flex items-center gap-1 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 border border-yellow-200 rounded-full",children:"⏰ C\xf2n 15 ng\xe0y"})]})]})]})]}),(0,d.jsxs)("div",{className:"flex justify-end gap-4 p-6 border-t border-gray-200",children:[(0,d.jsx)("button",{type:"button",onClick:b,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"}),(0,d.jsx)("button",{type:"submit",onClick:j,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Cập nhật"})]})]})}):null}var T=c(91292),U=c(75219);function V({date:a,warningDays:b=30,dangerDays:c=7,showIcon:f=!0,showDetailed:g=!1,size:h="md",className:i=""}){let[j,k]=(0,e.useState)(null),[l,m]=(0,e.useState)("none"),o=()=>{if(!j)return"Kh\xf4ng c\xf3 dữ liệu";if(j.isExpired)return"Đ\xe3 hết hạn";let b=new Date(a),c=new Date,d=new Date(c.getTime()+6e4*c.getTimezoneOffset()+252e5);b.setHours(0,0,0,0),d.setHours(0,0,0,0);let e=b.getTime()-d.getTime();if(e>0){let a=Math.ceil(e/864e5);return`C\xf2n ${a} ng\xe0y`}if(1===j.totalDays)return"C\xf2n 1 ng\xe0y";{if(j.totalDays>1)return`C\xf2n ${j.totalDays} ng\xe0y`;if(0===j.totalDays)return"Hết hạn h\xf4m nay";let a=Math.abs(j.totalDays);return`Qu\xe1 hạn ${a} ng\xe0y`}};if(!a||!j)return null;let p={expired:{bg:"bg-red-100",text:"text-red-800",border:"border-red-200",icon:T.A,color:"text-red-600"},danger:{bg:"bg-red-50",text:"text-red-700",border:"border-red-300",icon:P.A,color:"text-red-500"},warning:{bg:"bg-yellow-50",text:"text-yellow-700",border:"border-yellow-300",icon:P.A,color:"text-yellow-500"},safe:{bg:"bg-green-50",text:"text-green-700",border:"border-green-300",icon:U.A,color:"text-green-500"},none:{bg:"bg-gray-50",text:"text-gray-700",border:"border-gray-300",icon:n.A,color:"text-gray-500"}}[l],q={sm:{container:"px-2 py-1 text-xs",icon:12,gap:"gap-1"},md:{container:"px-3 py-1.5 text-sm",icon:14,gap:"gap-1.5"},lg:{container:"px-4 py-2 text-base",icon:16,gap:"gap-2"}}[h],r=p.icon;return(0,d.jsxs)("span",{className:`
        inline-flex items-center ${q.gap} ${q.container}
        ${p.bg} ${p.text} ${p.border}
        border rounded-full font-medium
        ${i}
      `,title:`${o()} - Hạn: ${new Date(a).toLocaleDateString("vi-VN",{timeZone:"Asia/Ho_Chi_Minh",year:"numeric",month:"2-digit",day:"2-digit"})} (GMT+7)`,children:[f&&(0,d.jsx)(r,{size:q.icon,className:p.color}),o()]})}let W=[{name:"ngayNhanVanThu",label:"Ng\xe0y nhận văn thư"},{name:"ngayThuLy",label:"Ng\xe0y thụ l\xfd"},{name:"ngayBanHanh",label:"Ng\xe0y ban h\xe0nh"}];function X({targetModel:a="CourtCase"}){let[b,c]=(0,e.useState)([]),[f,g]=(0,e.useState)([]),[h,i]=(0,e.useState)([]),[k,l]=(0,e.useState)(!0),[o,p]=(0,e.useState)(null),[q,r]=(0,e.useState)(!1),s=async()=>{try{l(!0);let b=localStorage.getItem("sessionToken")||"",[d,e]=await Promise.all([Q.A.getDateCountdowns(a,b),D.A.getCustomFields(a,b)]);d.payload.success&&c(d.payload.countdowns),e.payload.success&&g(e.payload.fields);let f=e.payload.success?e.payload.fields.filter(a=>"date"===a.dataType||"datetime"===a.dataType):[],h=[...W.map(a=>({...a,isDefault:!0})),...f.map(a=>({name:a.name,label:a.label,isDefault:!1}))];i(h)}catch(a){console.error("Error fetching data:",a),j.oR.error("Lỗi khi tải dữ liệu")}finally{l(!1)}},t=async(b,c,d)=>{try{let e=localStorage.getItem("sessionToken")||"",f=await Q.A.upsertDateCountdown({targetModel:a,fieldName:b,isBuiltIn:c,countdownConfig:d},e);f.payload.success?(j.oR.success("Tạo cấu h\xecnh đếm ngược th\xe0nh c\xf4ng"),r(!1),s()):j.oR.error(f.payload.message||"Kh\xf4ng thể tạo cấu h\xecnh đếm ngược")}catch(a){console.error("Error creating countdown:",a),j.oR.error("Lỗi khi tạo cấu h\xecnh đếm ngược")}},u=async(c,d)=>{try{let e=localStorage.getItem("sessionToken")||"",f=b.find(a=>a._id===c);if(!f)return;let g=await Q.A.upsertDateCountdown({targetModel:a,fieldName:f.fieldName,isBuiltIn:f.isBuiltIn,countdownConfig:d},e);g.payload.success?(j.oR.success("Cập nhật cấu h\xecnh đếm ngược th\xe0nh c\xf4ng"),p(null),s()):j.oR.error(g.payload.message||"Kh\xf4ng thể cập nhật cấu h\xecnh đếm ngược")}catch(a){console.error("Error updating countdown:",a),j.oR.error("Lỗi khi cập nhật cấu h\xecnh đếm ngược")}},v=async a=>{if(confirm("Bạn c\xf3 chắc chắn muốn x\xf3a cấu h\xecnh đếm ngược n\xe0y?"))try{let b=localStorage.getItem("sessionToken")||"",c=await Q.A.deleteDateCountdown(a,b);c.payload.success?(j.oR.success("X\xf3a cấu h\xecnh đếm ngược th\xe0nh c\xf4ng"),s()):j.oR.error(c.payload.message||"Kh\xf4ng thể x\xf3a cấu h\xecnh đếm ngược")}catch(a){console.error("Error deleting countdown:",a),j.oR.error("Lỗi khi x\xf3a cấu h\xecnh đếm ngược")}},y=a=>{let b=h.find(b=>b.name===a);return b?b.label:a},z=()=>{let a=b.map(a=>a.fieldName);return h.filter(b=>!a.includes(b.name))};return k?(0,d.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 flex items-center gap-2",children:[(0,d.jsx)(n.A,{size:20}),"Cấu h\xecnh đếm ngược ng\xe0y hết hạn"]}),(0,d.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Thiết lập cảnh b\xe1o v\xe0 đếm ngược cho c\xe1c trường ng\xe0y th\xe1ng (m\xfai giờ GMT+7)"}),(0,d.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-3",children:(0,d.jsxs)("div",{className:"flex items-start gap-2",children:[(0,d.jsx)("span",{className:"text-yellow-600 mt-0.5",children:"\uD83D\uDCA1"}),(0,d.jsxs)("div",{className:"text-sm text-yellow-800",children:[(0,d.jsx)("strong",{children:"C\xe1ch hoạt động:"})," Ng\xe0y được chọn l\xe0 ng\xe0y bắt đầu đếm ngược, cộng số ng\xe0y cảnh b\xe1o để t\xednh ng\xe0y hết hạn.",(0,d.jsx)("br",{}),(0,d.jsx)("strong",{children:"V\xed dụ:"})," Ng\xe0y bắt đầu 31/07/2025 + 90 ng\xe0y cảnh b\xe1o = Hết hạn 29/10/2025"]})]})}),(0,d.jsxs)("div",{className:"flex items-center gap-2 mt-2",children:[(0,d.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:"\uD83C\uDF0F GMT+7 (Việt Nam)"}),(0,d.jsx)("span",{className:"text-xs text-gray-500",children:"Đếm ngược theo thời gian thực với độ ch\xednh x\xe1c đến gi\xe2y"})]})]}),(0,d.jsxs)("button",{onClick:()=>r(!0),disabled:0===z().length,className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,d.jsx)(x,{size:16}),"Th\xeam cấu h\xecnh"]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,d.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,d.jsx)(m,{size:20,className:"text-blue-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-blue-900",children:h.length}),(0,d.jsx)("div",{className:"text-sm text-blue-700",children:"Trường ng\xe0y"})]})]})}),(0,d.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,d.jsx)(n.A,{size:20,className:"text-green-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-green-900",children:b.filter(a=>a.countdownConfig.enabled).length}),(0,d.jsx)("div",{className:"text-sm text-green-700",children:"Đang hoạt động"})]})]})}),(0,d.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,d.jsx)(P.A,{size:20,className:"text-yellow-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-yellow-900",children:b.filter(a=>a.countdownConfig.emailNotification.enabled).length}),(0,d.jsx)("div",{className:"text-sm text-yellow-700",children:"Email cảnh b\xe1o"})]})]})}),(0,d.jsx)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,d.jsx)(w.A,{size:20,className:"text-purple-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-purple-900",children:b.length}),(0,d.jsx)("div",{className:"text-sm text-purple-700",children:"Tổng cấu h\xecnh"})]})]})})]}),0===b.length?(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(n.A,{size:48,className:"mx-auto text-gray-400 mb-4"}),(0,d.jsx)("p",{className:"text-gray-500 mb-4",children:"Chưa c\xf3 cấu h\xecnh đếm ngược n\xe0o"}),(0,d.jsx)("button",{onClick:()=>r(!0),disabled:0===z().length,className:"text-blue-600 hover:text-blue-700 font-medium disabled:text-gray-400",children:0===z().length?"Kh\xf4ng c\xf3 trường ng\xe0y n\xe0o để cấu h\xecnh":"Tạo cấu h\xecnh đầu ti\xean"})]}):(0,d.jsx)("div",{className:"space-y-4",children:b.map(a=>(0,d.jsx)("div",{className:`p-4 border rounded-lg ${a.countdownConfig.enabled?"border-green-200 bg-green-50":"border-gray-200 bg-gray-50"}`,children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("div",{className:"flex-1",children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:`p-2 rounded-lg ${a.countdownConfig.enabled?"bg-green-100":"bg-gray-100"}`,children:(0,d.jsx)(n.A,{size:16,className:a.countdownConfig.enabled?"text-green-600":"text-gray-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900",children:y(a.fieldName)}),(0,d.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600 mt-1",children:[(0,d.jsxs)("span",{children:["Cảnh b\xe1o: ",a.countdownConfig.warningDays," ng\xe0y"]}),(0,d.jsxs)("span",{children:["Nguy hiểm: ",a.countdownConfig.dangerDays," ng\xe0y"]}),a.countdownConfig.emailNotification.enabled&&(0,d.jsx)("span",{className:"text-blue-600",children:"\uD83D\uDCE7 Email"})]}),a.countdownConfig.enabled&&(0,d.jsxs)("div",{className:"mt-2",children:[(0,d.jsx)("span",{className:"text-xs text-gray-500 mr-2",children:"Preview (đang đếm ngược):"}),(0,d.jsx)(V,{date:new Date(Date.now()+24*a.countdownConfig.dangerDays*36e5),warningDays:a.countdownConfig.warningDays,dangerDays:a.countdownConfig.dangerDays,size:"sm",showIcon:!0,showDetailed:!0}),(0,d.jsxs)("span",{className:"text-xs text-gray-500 ml-2",children:["(Ng\xe0y bắt đầu + ",a.countdownConfig.warningDays," ng\xe0y = Ng\xe0y hết hạn)"]})]})]})]})}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:`px-2 py-1 text-xs rounded-full ${a.countdownConfig.enabled?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:a.countdownConfig.enabled?"Hoạt động":"Tắt"}),(0,d.jsx)("button",{onClick:()=>p(a),className:"p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",title:"Chỉnh sửa",children:(0,d.jsx)(B.A,{size:16})}),(0,d.jsx)("button",{onClick:()=>v(a._id),className:"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors",title:"X\xf3a",children:(0,d.jsx)(C.A,{size:16})})]})]})},a._id))}),(0,d.jsx)(R,{isOpen:q,onClose:()=>r(!1),onSubmit:t,availableFields:z()}),(0,d.jsx)(S,{isOpen:!!o,onClose:()=>p(null),onSubmit:u,countdown:o,getFieldLabel:y})]})}var Y=c(74701);function Z(){let a=(0,i.useRouter)(),[b,c]=(0,e.useState)("fields"),[f,j]=(0,e.useState)([]);return(0,d.jsx)(Y.default,{requiredPermission:"custom_fields_view",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,d.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,d.jsx)("button",{onClick:()=>a.back(),className:"p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:(0,d.jsx)(g,{size:20})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Quản l\xfd trường t\xf9y chỉnh - Vụ việc t\xf2a \xe1n"}),(0,d.jsx)("p",{className:"text-gray-600 mt-1",children:"Tạo v\xe0 quản l\xfd c\xe1c trường dữ liệu t\xf9y chỉnh cho vụ việc t\xf2a \xe1n, giống như Excel"})]})]}),(0,d.jsxs)("div",{className:"flex space-x-1 mb-6",children:[(0,d.jsx)("button",{onClick:()=>c("fields"),className:`px-4 py-2 rounded-lg font-medium transition-colors ${"fields"===b?"bg-blue-600 text-white":"text-gray-600 hover:bg-gray-100"}`,children:"Quản l\xfd trường"}),(0,d.jsxs)("button",{onClick:()=>c("stats"),className:`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${"stats"===b?"bg-blue-600 text-white":"text-gray-600 hover:bg-gray-100"}`,children:[(0,d.jsx)(h.A,{size:16}),"Thống k\xea"]}),(0,d.jsx)("button",{onClick:()=>c("countdown"),className:`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${"countdown"===b?"bg-blue-600 text-white":"text-gray-600 hover:bg-gray-100"}`,children:"⏰ Đếm ngược"})]}),"fields"===b?(0,d.jsx)(K,{targetModel:"CourtCase",onFieldsChange:a=>{j(a)}}):"stats"===b?(0,d.jsx)(O,{targetModel:"CourtCase",fields:f}):(0,d.jsx)(X,{targetModel:"CourtCase"}),(0,d.jsxs)("div",{className:"mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-2",children:"\uD83D\uDCA1 T\xednh năng giống Excel"}),(0,d.jsxs)("div",{className:"text-blue-800 space-y-2",children:[(0,d.jsxs)("p",{children:["• ",(0,d.jsx)("strong",{children:"Tự động tạo cột:"})," Th\xeam trường mới sẽ tự động tạo cột trong bảng danh s\xe1ch"]}),(0,d.jsxs)("p",{children:["• ",(0,d.jsx)("strong",{children:"Nhiều kiểu dữ liệu:"})," Văn bản, số, ng\xe0y th\xe1ng, lựa chọn, tiền tệ, v.v."]}),(0,d.jsxs)("p",{children:["• ",(0,d.jsx)("strong",{children:"Thống k\xea tự động:"})," Hệ thống tự động t\xednh to\xe1n thống k\xea cho c\xe1c trường mới"]}),(0,d.jsxs)("p",{children:["• ",(0,d.jsx)("strong",{children:"K\xe9o thả sắp xếp:"})," Thay đổi thứ tự hiển thị cột bằng c\xe1ch k\xe9o thả"]}),(0,d.jsxs)("p",{children:["• ",(0,d.jsx)("strong",{children:"T\xf9y chỉnh hiển thị:"})," Ẩn/hiện cột, điều chỉnh độ rộng"]}),(0,d.jsxs)("p",{children:["• ",(0,d.jsx)("strong",{children:"Validation tự động:"})," Kiểm tra dữ liệu theo quy tắc đ\xe3 định"]})]})]})]})})}},55511:a=>{"use strict";a.exports=require("crypto")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69506:(a,b,c)=>{Promise.resolve().then(c.bind(c,14621))},69852:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>D.a,__next_app__:()=>J,handler:()=>L,pages:()=>I,routeModule:()=>K,tree:()=>H});var d=c(49754),e=c(9117),f=c(46595),g=c(32324),h=c(39326),i=c(38928),j=c(20175),k=c(12),l=c(54290),m=c(12696),n=c(82802),o=c(77533),p=c(45229),q=c(32822),r=c(261),s=c(26453),t=c(52474),u=c(26713),v=c(51356),w=c(62685),x=c(36225),y=c(63446),z=c(2762),A=c(45742),B=c(86439),C=c(81170),D=c.n(C),E=c(62506),F=c(91203),G={};for(let a in E)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(G[a]=()=>E[a]);c.d(b,G);let H={children:["",{children:["(private)",{children:["dashboard",{children:["court-cases",{children:["custom-fields",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,14621)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\court-cases\\custom-fields\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,87473)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,32768,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,51472)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,5682)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,81170,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.bind(c,59732)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,32768,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,I=["C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\court-cases\\custom-fields\\page.tsx"],J={require:c,loadChunk:()=>Promise.resolve()},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(private)/dashboard/court-cases/custom-fields/page",pathname:"/dashboard/court-cases/custom-fields",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:H},distDir:".next",relativeProjectDir:""});async function L(a,b,d){var C;let G="/(private)/dashboard/court-cases/custom-fields/page";"/index"===G&&(G="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:G,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(G),{isOnDemandRevalidate:ah}=O,ai=K.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(C=$.routes[ag]??$.dynamicRoutes[ag])?void 0:C.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===K.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&K.isDev&&(aB=aa),K.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...E,tree:H,pages:I,GlobalError:D(),handler:L,routeModule:K,__next_app__:J};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:G,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=K.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:K,page:G,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),K.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!K.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===K.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await K.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!K.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&F.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:G,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},72286:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(65424);let e={getCustomFields:(a="CourtCase",b)=>d.Ay.get(`/api/custom-fields?targetModel=${a}`,{headers:{Authorization:`Bearer ${b}`}}),createCustomField:(a,b)=>d.Ay.post("/api/custom-fields",a,{headers:{Authorization:`Bearer ${b}`}}),updateCustomField:(a,b,c)=>d.Ay.put(`/api/custom-fields/${a}`,b,{headers:{Authorization:`Bearer ${c}`}}),deleteCustomField:(a,b)=>d.Ay.delete(`/api/custom-fields/${a}`,{headers:{Authorization:`Bearer ${b}`}}),updateFieldsOrder:(a,b)=>d.Ay.put("/api/custom-fields/order",{fieldOrders:a},{headers:{Authorization:`Bearer ${b}`}}),getCustomFieldStats:(a="CourtCase",b)=>d.Ay.get(`/api/custom-fields/stats?targetModel=${a}`,{headers:{Authorization:`Bearer ${b}`}}),getFieldsInDatabase:(a="CourtCase")=>d.Ay.get(`/api/custom-fields/database-fields?targetModel=${a}`)}},74701:(a,b,c)=>{"use strict";c.d(b,{default:()=>g});var d=c(21124),e=c(25816),f=c(42378);function g({children:a,requiredPermission:b,requiredPermissions:c=[],requireAll:g=!1,fallbackPath:h="/dashboard"}){let{hasPermission:i,hasAnyPermission:j,isAdmin:k,isDepartmentManager:l,isLoading:m}=(0,e.S)(),n=(0,f.useRouter)();if(m)return(0,d.jsx)("div",{className:"flex justify-center items-center min-h-[200px]",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})});if(k)return(0,d.jsx)(d.Fragment,{children:a});return(b?"admin"===b&&!!l||i(b):!(c.length>0)||(g?c.every(a=>i(a)):j(c)))?(0,d.jsx)(d.Fragment,{children:a}):(0,d.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Kh\xf4ng c\xf3 quyền truy cập"}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:"Bạn kh\xf4ng c\xf3 quyền truy cập v\xe0o trang n\xe0y."}),(0,d.jsx)("button",{onClick:()=>n.back(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Quay lại"})]})})}c(38301)},75219:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},76180:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},79428:a=>{"use strict";a.exports=require("buffer")},80517:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(65424);let e={getFieldConfiguration:(a="CourtCase",b)=>d.Ay.get(`/api/field-configurations?targetModel=${a}`,{headers:{Authorization:`Bearer ${b}`}}),updateFieldConfiguration:(a,b)=>d.Ay.put("/api/field-configurations",a,{headers:{Authorization:`Bearer ${b}`}}),updateFieldOrder:(a,b)=>d.Ay.put("/api/field-configurations/order",a,{headers:{Authorization:`Bearer ${b}`}}),updateFieldVisibility:(a,b)=>d.Ay.put("/api/field-configurations/visibility",a,{headers:{Authorization:`Bearer ${b}`}})}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},87658:(a,b,c)=>{Promise.resolve().then(c.bind(c,52701))},90828:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(65424);let e={getDateCountdowns:(a="CourtCase",b)=>d.Ay.get(`/api/date-countdowns?targetModel=${a}`,{headers:{Authorization:`Bearer ${b}`}}),upsertDateCountdown:(a,b)=>d.Ay.post("/api/date-countdowns",a,{headers:{Authorization:`Bearer ${b}`}}),deleteDateCountdown:(a,b)=>d.Ay.delete(`/api/date-countdowns/${a}`,{headers:{Authorization:`Bearer ${b}`}}),getCountdownStats:(a="CourtCase",b)=>d.Ay.get(`/api/date-countdowns/stats?targetModel=${a}`,{headers:{Authorization:`Bearer ${b}`}}),getUpcomingDeadlines:(a="CourtCase",b=30,c)=>d.Ay.get(`/api/date-countdowns/upcoming?targetModel=${a}&days=${b}`,{headers:{Authorization:`Bearer ${c}`}})}},91292:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},94684:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}};var b=require("../../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[5873,1428,4479,6975,3445,3271],()=>b(b.s=69852));module.exports=c})();