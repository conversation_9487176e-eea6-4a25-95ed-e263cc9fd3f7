import http from "@/lib/http";

export interface CountdownConfig {
  enabled: boolean;
  warningDays: number;
  dangerDays: number;
  safeDays: number;
  showInList: boolean;
  showCountdownBadge: boolean;
  showColorWarning: boolean;
  hideExpired: boolean;
  emailNotification: {
    enabled: boolean;
    daysBefore: number;
  };
}

export interface DateCountdown {
  _id: string;
  targetModel: string;
  fieldName: string;
  isBuiltIn: boolean;
  countdownConfig: CountdownConfig;
  createdBy: {
    _id: string;
    username: string;
    email: string;
  };
  updatedBy?: {
    _id: string;
    username: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface UpsertDateCountdownBody {
  targetModel: string;
  fieldName: string;
  isBuiltIn?: boolean;
  countdownConfig: Partial<CountdownConfig>;
}

export interface CountdownStats {
  [fieldName: string]: {
    fieldName: string;
    label: string;
    config: CountdownConfig;
    total: number;
    expired: number;
    danger: number;
    warning: number;
    safe: number;
    upcoming: Array<{
      recordId: string;
      daysRemaining: number;
      status: string;
      date: string;
    }>;
  };
}

export interface UpcomingDeadline {
  recordId: string;
  record: any;
  fieldName: string;
  fieldLabel: string;
  date: string;
  daysRemaining: number;
  status: 'expired' | 'danger' | 'warning' | 'safe';
  countdownText: string;
  colors: {
    bg: string;
    text: string;
    border: string;
  };
}

const dateCountdownApiRequest = {
  // Lấy danh sách cấu hình đếm ngược
  getDateCountdowns: (targetModel: string = 'CourtCase', sessionToken: string) =>
    http.get<{ success: boolean; countdowns: DateCountdown[] }>(
      `/api/date-countdowns?targetModel=${targetModel}`,
      {
        headers: {
          Authorization: `Bearer ${sessionToken}`,
        },
      }
    ),

  // Tạo hoặc cập nhật cấu hình đếm ngược
  upsertDateCountdown: (body: UpsertDateCountdownBody, sessionToken: string) =>
    http.post<{ success: boolean; message: string; countdown: DateCountdown }>(
      "/api/date-countdowns",
      body,
      {
        headers: {
          Authorization: `Bearer ${sessionToken}`,
        },
      }
    ),

  // Xóa cấu hình đếm ngược
  deleteDateCountdown: (id: string, sessionToken: string) =>
    http.delete<{ success: boolean; message: string }>(
      `/api/date-countdowns/${id}`,
      {
        headers: {
          Authorization: `Bearer ${sessionToken}`,
        },
      }
    ),

  // Lấy thống kê đếm ngược
  getCountdownStats: (targetModel: string = 'CourtCase', sessionToken: string) =>
    http.get<{ success: boolean; stats: CountdownStats }>(
      `/api/date-countdowns/stats?targetModel=${targetModel}`,
      {
        headers: {
          Authorization: `Bearer ${sessionToken}`,
        },
      }
    ),

  // Lấy danh sách sắp hết hạn
  getUpcomingDeadlines: (targetModel: string = 'CourtCase', days: number = 30, sessionToken: string) =>
    http.get<{ success: boolean; deadlines: UpcomingDeadline[] }>(
      `/api/date-countdowns/upcoming?targetModel=${targetModel}&days=${days}`,
      {
        headers: {
          Authorization: `Bearer ${sessionToken}`,
        },
      }
    ),
};

export default dateCountdownApiRequest;
