"use client";

import { useState } from "react";
import { X, Clock, AlertTriangle, Mail } from "lucide-react";
import { CountdownConfig } from "@/apiRequests/dateCountdown";

interface CreateDateCountdownModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (fieldName: string, isDefault: boolean, config: Partial<CountdownConfig>) => void;
  availableFields: Array<{ name: string; label: string; isDefault: boolean }>;
}

export default function CreateDateCountdownModal({
  isOpen,
  onClose,
  onSubmit,
  availableFields
}: CreateDateCountdownModalProps) {
  const [selectedField, setSelectedField] = useState('');
  const [config, setConfig] = useState<Partial<CountdownConfig>>({
    enabled: true,
    warningDays: 30,
    dangerDays: 7,
    safeDays: 90,
    showInList: true,
    showCountdownBadge: true,
    showColorWarning: true,
    hideExpired: false,
    emailNotification: {
      enabled: false,
      daysBefore: 7
    }
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedField) {
      alert('Vui lòng chọn trường ngày');
      return;
    }

    if (config.warningDays && config.dangerDays && config.dangerDays >= config.warningDays) {
      alert('Số ngày nguy hiểm phải nhỏ hơn số ngày cảnh báo');
      return;
    }

    if (config.safeDays && config.warningDays && config.safeDays <= config.warningDays) {
      alert('Số ngày an toàn phải lớn hơn số ngày cảnh báo');
      return;
    }

    const field = availableFields.find(f => f.name === selectedField);
    if (field) {
      onSubmit(field.name, field.isDefault, config);
    }
  };

  const handleReset = () => {
    setSelectedField('');
    setConfig({
      enabled: true,
      warningDays: 30,
      dangerDays: 7,
      safeDays: 90,
      showInList: true,
      showCountdownBadge: true,
      showColorWarning: true,
      hideExpired: false,
      emailNotification: {
        enabled: false,
        daysBefore: 7
      }
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Clock size={20} className="text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                Tạo cấu hình đếm ngược
              </h3>
              <p className="text-sm text-gray-600">
                Thiết lập cảnh báo hết hạn cho trường ngày (GMT+7)
              </p>
              <div className="bg-blue-50 border border-blue-200 rounded p-2 mt-2">
                <p className="text-xs text-blue-700">
                  💡 <strong>Lưu ý:</strong> Ngày được chọn là ngày bắt đầu đếm ngược, hệ thống sẽ cộng số ngày cảnh báo để tính ngày hết hạn
                </p>
              </div>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors p-2 hover:bg-gray-100 rounded-lg"
          >
            <X size={20} />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* Chọn trường */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Chọn trường ngày <span className="text-red-500">*</span>
            </label>
            <select
              value={selectedField}
              onChange={(e) => setSelectedField(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            >
              <option value="">-- Chọn trường ngày --</option>
              {availableFields.map((field) => (
                <option key={field.name} value={field.name}>
                  {field.label} {field.isDefault ? '(Cơ bản)' : '(Tùy chỉnh)'}
                </option>
              ))}
            </select>
          </div>

          {/* Cấu hình cơ bản */}
          <div className="space-y-4">
            <h4 className="text-md font-medium text-gray-900 flex items-center gap-2">
              <AlertTriangle size={16} />
              Cấu hình cảnh báo
            </h4>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Số ngày an toàn
                </label>
                <input
                  type="number"
                  min="1"
                  max="365"
                  value={config.safeDays || 90}
                  onChange={(e) => setConfig(prev => ({ ...prev, safeDays: parseInt(e.target.value) }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <p className="text-xs text-gray-500 mt-1">Hiển thị màu xanh khi còn nhiều hơn X ngày</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Số ngày cảnh báo
                </label>
                <input
                  type="number"
                  min="1"
                  max="365"
                  value={config.warningDays || 30}
                  onChange={(e) => setConfig(prev => ({ ...prev, warningDays: parseInt(e.target.value) }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <p className="text-xs text-gray-500 mt-1">Hiển thị cảnh báo vàng khi còn X ngày</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Số ngày nguy hiểm
                </label>
                <input
                  type="number"
                  min="1"
                  max="365"
                  value={config.dangerDays || 7}
                  onChange={(e) => setConfig(prev => ({ ...prev, dangerDays: parseInt(e.target.value) }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <p className="text-xs text-gray-500 mt-1">Hiển thị cảnh báo đỏ khi còn X ngày</p>
              </div>
            </div>
          </div>

          {/* Cấu hình hiển thị */}
          <div className="space-y-4">
            <h4 className="text-md font-medium text-gray-900">Cấu hình hiển thị</h4>
            
            <div className="space-y-3">
              <label className="flex items-center gap-3">
                <input
                  type="checkbox"
                  checked={config.enabled}
                  onChange={(e) => setConfig(prev => ({ ...prev, enabled: e.target.checked }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <div>
                  <span className="text-sm font-medium">Bật đếm ngược</span>
                  <p className="text-xs text-gray-500">Kích hoạt tính năng đếm ngược cho trường này</p>
                </div>
              </label>

              <label className="flex items-center gap-3">
                <input
                  type="checkbox"
                  checked={config.showCountdownBadge}
                  onChange={(e) => setConfig(prev => ({ ...prev, showCountdownBadge: e.target.checked }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <div>
                  <span className="text-sm font-medium">Hiển thị badge đếm ngược</span>
                  <p className="text-xs text-gray-500">Hiển thị đếm ngược chi tiết (ngày, giờ, phút, giây) theo GMT+7</p>
                </div>
              </label>

              <label className="flex items-center gap-3">
                <input
                  type="checkbox"
                  checked={config.showColorWarning}
                  onChange={(e) => setConfig(prev => ({ ...prev, showColorWarning: e.target.checked }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <div>
                  <span className="text-sm font-medium">Hiển thị màu cảnh báo</span>
                  <p className="text-xs text-gray-500">Thay đổi màu sắc theo trạng thái (xanh/vàng/đỏ)</p>
                </div>
              </label>

              <label className="flex items-center gap-3">
                <input
                  type="checkbox"
                  checked={config.hideExpired}
                  onChange={(e) => setConfig(prev => ({ ...prev, hideExpired: e.target.checked }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <div>
                  <span className="text-sm font-medium">Ẩn nếu quá hạn</span>
                  <p className="text-xs text-gray-500">Tự động ẩn badge khi đã quá hạn</p>
                </div>
              </label>
            </div>
          </div>

          {/* Email notification */}
          <div className="space-y-4">
            <h4 className="text-md font-medium text-gray-900 flex items-center gap-2">
              <Mail size={16} />
              Thông báo email (Tùy chọn)
            </h4>
            
            <label className="flex items-center gap-3">
              <input
                type="checkbox"
                checked={config.emailNotification?.enabled}
                onChange={(e) => setConfig(prev => ({
                  ...prev,
                  emailNotification: {
                    ...prev.emailNotification,
                    enabled: e.target.checked
                  }
                }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <div>
                <span className="text-sm font-medium">Gửi email cảnh báo</span>
                <p className="text-xs text-gray-500">Tự động gửi email thông báo trước hạn</p>
              </div>
            </label>

            {config.emailNotification?.enabled && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Gửi email trước (ngày)
                </label>
                <input
                  type="number"
                  min="1"
                  max="365"
                  value={config.emailNotification?.daysBefore || 7}
                  onChange={(e) => setConfig(prev => ({
                    ...prev,
                    emailNotification: {
                      ...prev.emailNotification,
                      daysBefore: parseInt(e.target.value)
                    }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            )}
          </div>

          {/* Preview */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h5 className="text-sm font-medium text-gray-900 mb-2">Xem trước:</h5>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">Ngày:</span>
                <span className="text-sm">25/12/2024</span>
              </div>
              {config.showCountdownBadge && (
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">Badge:</span>
                  <span className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 border border-yellow-200 rounded-full">
                    ⏰ Còn 15 ngày
                  </span>
                </div>
              )}
            </div>
          </div>
        </form>

        {/* Footer */}
        <div className="flex justify-end gap-4 p-6 border-t border-gray-200">
          <button
            type="button"
            onClick={() => {
              handleReset();
              onClose();
            }}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
          >
            Hủy
          </button>
          <button
            type="submit"
            form="countdown-form"
            onClick={handleSubmit}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Tạo cấu hình
          </button>
        </div>
      </div>
    </div>
  );
}
