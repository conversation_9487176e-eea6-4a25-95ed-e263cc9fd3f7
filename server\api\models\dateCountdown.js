const mongoose = require('mongoose');

const dateCountdownSchema = new mongoose.Schema({
  // Model áp dụng
  targetModel: {
    type: String,
    required: true,
    enum: ['CourtCase', 'User', 'Department']
  },
  
  // Tên trường ngày
  fieldName: {
    type: String,
    required: true
  },
  
  // Loại trường (built-in hoặc custom)
  isBuiltIn: {
    type: Boolean,
    default: false
  },
  
  // Cấu hình đếm ngược
  countdownConfig: {
    // Bật/tắt đếm ngược
    enabled: {
      type: Boolean,
      default: true
    },
    
    // Số ngày cảnh báo trước (mặc định 30 ngày)
    warningDays: {
      type: Number,
      default: 30,
      min: 1,
      max: 365
    },
    
    // Số ngày nguy hiểm (mặc định 7 ngày)
    dangerDays: {
      type: Number,
      default: 7,
      min: 1,
      max: 365
    },

    // Số ngày an toàn (mặc định 90 ngày) - hiển thị màu xanh khi còn nhiều hơn X ngày
    safeDays: {
      type: Number,
      default: 90,
      min: 1,
      max: 365
    },
    
    // Hiển thị trong danh sách
    showInList: {
      type: Boolean,
      default: true
    },
    
    // Hiển thị badge đếm ngược
    showCountdownBadge: {
      type: Boolean,
      default: true
    },
    
    // Hiển thị màu sắc cảnh báo
    showColorWarning: {
      type: Boolean,
      default: true
    },
    
    // Tự động ẩn nếu đã quá hạn
    hideExpired: {
      type: Boolean,
      default: false
    },
    
    // Thông báo email trước hạn
    emailNotification: {
      enabled: {
        type: Boolean,
        default: false
      },
      daysBefore: {
        type: Number,
        default: 7
      }
    }
  },
  
  // Metadata
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  }
}, {
  timestamps: true
});

// Indexes
dateCountdownSchema.index({ targetModel: 1, fieldName: 1 });
dateCountdownSchema.index({ targetModel: 1, fieldName: 1, createdBy: 1 }, { unique: true });

// Static method để tính số ngày còn lại
dateCountdownSchema.statics.calculateDaysRemaining = function(dateValue) {
  if (!dateValue) return null;
  
  const targetDate = new Date(dateValue);
  const currentDate = new Date();
  
  // Reset time to start of day for accurate calculation
  targetDate.setHours(0, 0, 0, 0);
  currentDate.setHours(0, 0, 0, 0);
  
  const timeDiff = targetDate.getTime() - currentDate.getTime();
  const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));
  
  return daysDiff;
};

// Static method để lấy trạng thái cảnh báo
dateCountdownSchema.statics.getWarningStatus = function(daysRemaining, warningDays, dangerDays) {
  if (daysRemaining === null) return 'none';
  
  if (daysRemaining < 0) return 'expired';
  if (daysRemaining <= dangerDays) return 'danger';
  if (daysRemaining <= warningDays) return 'warning';
  
  return 'safe';
};

// Static method để lấy màu sắc theo trạng thái
dateCountdownSchema.statics.getStatusColor = function(status) {
  const colors = {
    'expired': { bg: 'bg-red-100', text: 'text-red-800', border: 'border-red-200' },
    'danger': { bg: 'bg-red-100', text: 'text-red-800', border: 'border-red-200' },
    'warning': { bg: 'bg-yellow-100', text: 'text-yellow-800', border: 'border-yellow-200' },
    'safe': { bg: 'bg-green-100', text: 'text-green-800', border: 'border-green-200' },
    'none': { bg: 'bg-gray-100', text: 'text-gray-800', border: 'border-gray-200' }
  };
  
  return colors[status] || colors['none'];
};

// Static method để format hiển thị
dateCountdownSchema.statics.formatCountdown = function(daysRemaining, status) {
  if (daysRemaining === null) return '';
  
  if (daysRemaining < 0) {
    const overdueDays = Math.abs(daysRemaining);
    return `Quá hạn ${overdueDays} ngày`;
  }
  
  if (daysRemaining === 0) return 'Hết hạn hôm nay';
  if (daysRemaining === 1) return 'Còn 1 ngày';
  
  return `Còn ${daysRemaining} ngày`;
};

module.exports = mongoose.model('DateCountdown', dateCountdownSchema);
