{"/_not-found/page": "/_not-found", "/api/auth/login/route": "/api/auth/login", "/api/auth/active-mail/route": "/api/auth/active-mail", "/api/auth/logout/route": "/api/auth/logout", "/api/auth/active-authapp/route": "/api/auth/active-authapp", "/api/auth/route": "/api/auth", "/api/auth/user/profile/route": "/api/auth/user/profile", "/api/auth/verify-authapp/route": "/api/auth/verify-authapp", "/api/revalidate/route": "/api/revalidate", "/api/uploads/media/[...path]/route": "/api/uploads/media/[...path]", "/api/auth/user/route": "/api/auth/user", "/api/pdf-proxy/route": "/api/pdf-proxy", "/api/uploads/single/[...path]/route": "/api/uploads/single/[...path]", "/robots.txt/route": "/robots.txt", "/(auth)/forgot-pass/page": "/forgot-pass", "/(auth)/2fa/page": "/2fa", "/(auth)/change-password/page": "/change-password", "/(auth)/login/page": "/login", "/(auth)/register/page": "/register", "/(auth)/logout/page": "/logout", "/(auth)/verify/page": "/verify", "/author/page": "/author", "/logout-direct/page": "/logout-direct", "/page": "/", "/(private)/dashboard/court-cases/custom-fields/page": "/dashboard/court-cases/custom-fields", "/(private)/dashboard/account/page": "/dashboard/account", "/(private)/dashboard/departments/[id]/edit/page": "/dashboard/departments/[id]/edit", "/(private)/dashboard/departments/[id]/members/[memberId]/page": "/dashboard/departments/[id]/members/[memberId]", "/(private)/dashboard/departments/[id]/members/add/page": "/dashboard/departments/[id]/members/add", "/(private)/dashboard/departments/[id]/members/page": "/dashboard/departments/[id]/members", "/(private)/dashboard/court-cases/page": "/dashboard/court-cases", "/(private)/dashboard/departments/add/page": "/dashboard/departments/add", "/(private)/dashboard/departments/[id]/members/[memberId]/edit/page": "/dashboard/departments/[id]/members/[memberId]/edit", "/(private)/dashboard/files/page": "/dashboard/files", "/(private)/dashboard/departments/[id]/page": "/dashboard/departments/[id]", "/(private)/dashboard/departments/page": "/dashboard/departments", "/(private)/dashboard/page": "/dashboard", "/(private)/dashboard/user/[id]/page": "/dashboard/user/[id]", "/(private)/dashboard/setting/page": "/dashboard/setting", "/(private)/dashboard/user/import/page": "/dashboard/user/import", "/(private)/dashboard/user/add/page": "/dashboard/user/add", "/(private)/dashboard/user/log/[id]/page": "/dashboard/user/log/[id]", "/(private)/dashboard/user/page": "/dashboard/user"}